# Google Sheets Content Sync System

## 🚀 Overview

The Google Sheets Content Sync System enables seamless content management for the Astro-based rest area website. Non-technical users can update content through Google Sheets, while the system automatically maintains data integrity, multilingual support, and compatibility with the existing Astro Content Layer API.

## ✨ Features

- **🔄 Automated Synchronization**: Daily scheduled sync from Google Sheets to markdown files
- **🌍 Multilingual Support**: Full support for English and Polish content
- **✅ Data Validation**: Comprehensive validation against existing schema
- **🔒 Safe Updates**: Automatic backup system before content updates
- **🚀 GitHub Actions**: Fully automated workflow with error handling and notifications
- **📊 Rate Limiting**: Respects Google Sheets API limits with intelligent retry logic
- **🔍 Change Detection**: Only updates content when actual changes are detected
- **📋 Quality Assurance**: Built-in validation and Astro build testing
- **📅 Smart Dating**: Uses current generation date for all timestamp fields

## 🏗️ Architecture

```mermaid
graph TD
    A[Google Sheets] --> B[GitHub Actions Workflow]
    B --> C[Export Script]
    C --> D[JSON Files]
    D --> E[Data Validation]
    E --> F[Markdown Generation]
    F --> G[Astro Content Layer]
    G --> H[Website]
    
    I[Backup System] --> F
    J[Error Handling] --> B
    K[Rate Limiting] --> C
    L[Change Detection] --> B
```

## 📦 Installation

### 1. Install Dependencies

```bash
npm install googleapis
```

### 2. Environment Setup

Copy the environment template and configure:

```bash
cp .env.example .env
```

Required environment variables:
- `GOOGLE_SHEETS_ID`: Your Google Sheets document ID
- `GOOGLE_CREDENTIALS_JSON`: Service account credentials (for production)

### 3. Google Cloud Setup

1. **Create Google Cloud Project**
2. **Enable Google Sheets API**
3. **Create Service Account**
4. **Download credentials JSON**
5. **Share Google Sheet with service account email**

Detailed setup instructions: [docs/google-sheets-sync.md](docs/google-sheets-sync.md)

## 🎯 Quick Start

### Validate Setup

```bash
npm run sheets:validate
```

### Manual Sync (Development)

```bash
# Export from Google Sheets
npm run sheets:export

# Generate markdown files
npm run sheets:generate

# Or do both in one command
npm run sheets:sync
```

### Test Build

```bash
npm run build
```

## 📊 Google Sheets Structure

### Required Columns

| Column | Type | Description |
|--------|------|-------------|
| `rest_area_id` | Text | Unique identifier for the rest area |
| `title` | Text | Display name of the rest area |
| `latitude` | Number | GPS latitude coordinate |
| `longitude` | Number | GPS longitude coordinate |
| `road_class` | Text | Highway class (A, S, etc.) |
| `road_number` | Text | Highway number |
| `km_marker` | Text | Kilometer marker on highway |
| `region` | Text | Administrative region |
| `location` | Text | City or locality name |
| `country` | Text | Country name |

### Optional Columns

| Column | Type | Description |
|--------|------|-------------|
| `description_short` | Text | Brief description |
| `toilets_available` | Yes/No | Toilet facilities |
| `wifi` | Yes/No | WiFi availability |
| `gas_station_available` | Yes/No | Fuel station |
| `restaurant_bistro_available` | Yes/No | Restaurant/bistro |
| `showers_available` | Yes/No | Shower facilities |
| `ev_charging_station` | Yes/No | EV charging |
| `parking_spaces_cars` | Number | Car parking spaces |
| `parking_spaces_trucks_tir` | Number | Truck parking spaces |
| `parking_spaces_buses` | Number | Bus parking spaces |

[Complete column reference](docs/google-sheets-sync.md#required-column-structure)

## 🔄 Automated Workflow

### GitHub Actions

The system includes a comprehensive GitHub Actions workflow that:

1. **Validates Environment**: Checks credentials and configuration
2. **Exports Data**: Fetches latest data from Google Sheets
3. **Validates Content**: Ensures data integrity and completeness
4. **Generates Markdown**: Creates Astro-compatible markdown files
5. **Tests Build**: Validates Astro build with new content
6. **Commits Changes**: Automatically commits and pushes updates
7. **Error Handling**: Provides detailed error reporting and notifications

### Schedule

- **Daily**: Automatic sync at 6 AM UTC
- **Manual**: Trigger via GitHub Actions interface
- **On-Demand**: Run locally for development

## 🛠️ Available Scripts

| Script | Command | Description |
|--------|---------|-------------|
| Validate Setup | `npm run sheets:validate` | Test Google Sheets connection and configuration |
| Export Data | `npm run sheets:export` | Fetch data from Google Sheets to JSON |
| Generate Content | `npm run sheets:generate` | Convert JSON to markdown files |
| Full Sync | `npm run sheets:sync` | Export and generate in one command |
| Legacy Generate | `npm run content:generate` | Generate from CSV (existing method) |

## 🔍 Data Validation

### Automatic Validation

The system performs comprehensive validation:

- **Schema Compliance**: Validates against Astro Content Layer API schema
- **Required Fields**: Ensures all mandatory fields are present
- **Data Types**: Validates coordinates, numbers, and boolean values
- **Content Structure**: Checks markdown generation compatibility
- **Build Testing**: Verifies Astro can build with new content

### Error Reporting

- Detailed error messages with row numbers
- Warnings for data quality issues
- Summary reports for each sync operation
- GitHub Actions notifications for failures

## 🔒 Security & Safety

### Backup System

- Automatic backup before content updates
- Timestamped backup directories
- Easy restoration from backups
- Cleanup of old backup files

### Access Control

- Read-only access to Google Sheets
- Service account with minimal permissions
- Encrypted credential storage in GitHub Secrets
- No sensitive data in logs or outputs

### Change Detection

- Content hash comparison
- Only updates when changes detected
- Force update option for manual overrides
- Detailed change summaries

## 🌍 Multilingual Support

### Language Configuration

The system supports multiple languages with separate sheet tabs:

- **English**: `English!A:AZ` range
- **Polish**: `Polish!A:AZ` range

### Content Generation

- Language-specific markdown generation
- Localized content templates
- Proper URL structure for each language
- Maintains existing i18n compatibility

## 📈 Monitoring & Maintenance

### GitHub Actions Monitoring

- Workflow status in Actions tab
- Detailed logs for troubleshooting
- Email notifications for failures
- Success/failure summaries

### Content Quality

- Regular validation reports
- Data completeness metrics
- Translation accuracy checks
- Performance monitoring

## 🔧 Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```bash
   npm run sheets:validate
   ```

2. **Missing Columns**
   - Check Google Sheets column headers
   - Ensure exact spelling and case

3. **Data Validation Failures**
   - Review error messages in logs
   - Fix data in Google Sheets
   - Re-run sync

4. **Build Failures**
   - Check Astro compatibility
   - Validate markdown syntax
   - Review generated files

### Debug Mode

```bash
DEBUG=true npm run sheets:export
```

### Manual Recovery

```bash
# Restore from backup
cp -r temp/content-backup/TIMESTAMP/* src/content/rest-areas/

# Clear temporary files
rm -rf temp/

# Force full sync
npm run sheets:sync
```

## 📚 Documentation

- [Complete Setup Guide](docs/google-sheets-sync.md)
- [API Reference](src/utils/googleSheetsClient.ts)
- [Workflow Configuration](.github/workflows/sync-content.yml)
- [Validation Scripts](scripts/validate-sheets-setup.js)

## 🤝 Contributing

When modifying the sync system:

1. Test changes locally with `npm run sheets:validate`
2. Validate with sample data
3. Ensure backward compatibility
4. Update documentation
5. Test full workflow in a fork

## 📞 Support

For issues with the Google Sheets sync system:

1. Run validation: `npm run sheets:validate`
2. Check GitHub Actions logs
3. Review [troubleshooting guide](docs/google-sheets-sync.md#troubleshooting)
4. Validate Google Sheets structure
5. Test individual components manually

## 🎉 Benefits

### For Content Managers
- **Easy Updates**: Familiar Google Sheets interface
- **No Technical Knowledge**: Update content without coding
- **Real-time Collaboration**: Multiple editors can work simultaneously
- **Version History**: Google Sheets tracks all changes

### For Developers
- **Automated Workflow**: No manual content deployment
- **Data Integrity**: Built-in validation and error handling
- **Scalable**: Handles large datasets efficiently
- **Maintainable**: Clean separation of content and code

### For the Project
- **Reduced Maintenance**: Less manual content management
- **Improved Quality**: Consistent validation and formatting
- **Better Collaboration**: Non-technical team members can contribute
- **Faster Updates**: Automated deployment pipeline

---

**Ready to get started?** Follow the [complete setup guide](docs/google-sheets-sync.md) or run `npm run sheets:validate` to test your configuration!
