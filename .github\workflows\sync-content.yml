name: Sync Content from Google Sheets

on:
  # Schedule daily runs at 6 AM UTC
  schedule:
    - cron: '0 6 * * *'
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      force_update:
        description: 'Force update even if no changes detected'
        required: false
        default: 'false'
        type: boolean
      languages:
        description: 'Languages to sync (comma-separated: en,pl or leave empty for all)'
        required: false
        default: ''
        type: string

env:
  NODE_VERSION: '18'

jobs:
  sync-content:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        # Install additional dependencies for Google Sheets API
        npm install googleapis

    - name: Validate environment
      run: |
        if [ -z "${{ secrets.GOOGLE_CREDENTIALS_JSON }}" ]; then
          echo "❌ GOOGLE_CREDENTIALS_JSON secret is not set"
          exit 1
        fi
        if [ -z "${{ secrets.GOOGLE_SHEETS_ID }}" ]; then
          echo "❌ GOOGLE_SHEETS_ID secret is not set"
          exit 1
        fi
        echo "✅ Environment validation passed"

    - name: Create temp directories
      run: |
        mkdir -p temp/sheets-export
        mkdir -p temp/content-backup

    - name: Export data from Google Sheets
      env:
        GOOGLE_CREDENTIALS_JSON: ${{ secrets.GOOGLE_CREDENTIALS_JSON }}
        GOOGLE_SHEETS_ID: ${{ secrets.GOOGLE_SHEETS_ID }}
      run: |
        echo "📊 Exporting data from Google Sheets..."
        node scripts/export-from-sheets.js
        
        # Check if export was successful
        if [ ! -f "temp/sheets-export/export-summary.json" ]; then
          echo "❌ Export failed - no summary file found"
          exit 1
        fi
        
        # Display export summary
        echo "📋 Export Summary:"
        cat temp/sheets-export/export-summary.json | jq -r '.summary | "Total records: \(.totalRecords), Errors: \(.errors | length), Warnings: \(.warnings | length)"'

    - name: Check for content changes
      id: check_changes
      run: |
        echo "🔍 Checking for content changes..."
        
        # Create a hash of current content
        CURRENT_HASH=""
        if [ -d "src/content/rest-areas/en" ]; then
          CURRENT_HASH=$(find src/content/rest-areas -name "*.md" -type f -exec md5sum {} \; | sort | md5sum | cut -d' ' -f1)
        fi
        
        # Generate new content in temp directory
        node scripts/generate-from-json.js
        
        # Create a hash of new content
        NEW_HASH=""
        if [ -d "src/content/rest-areas/en" ]; then
          NEW_HASH=$(find src/content/rest-areas -name "*.md" -type f -exec md5sum {} \; | sort | md5sum | cut -d' ' -f1)
        fi
        
        echo "current_hash=$CURRENT_HASH" >> $GITHUB_OUTPUT
        echo "new_hash=$NEW_HASH" >> $GITHUB_OUTPUT
        
        if [ "$CURRENT_HASH" != "$NEW_HASH" ] || [ "${{ github.event.inputs.force_update }}" = "true" ]; then
          echo "has_changes=true" >> $GITHUB_OUTPUT
          echo "✅ Content changes detected or force update requested"
        else
          echo "has_changes=false" >> $GITHUB_OUTPUT
          echo "ℹ️  No content changes detected"
        fi

    - name: Validate generated content
      if: steps.check_changes.outputs.has_changes == 'true'
      run: |
        echo "🔍 Validating generated content..."
        
        # Check if files were generated
        EN_COUNT=$(find src/content/rest-areas/en -name "*.md" -type f | wc -l)
        PL_COUNT=$(find src/content/rest-areas/pl -name "*.md" -type f | wc -l)
        
        echo "Generated files - EN: $EN_COUNT, PL: $PL_COUNT"
        
        if [ "$EN_COUNT" -eq 0 ] && [ "$PL_COUNT" -eq 0 ]; then
          echo "❌ No markdown files were generated"
          exit 1
        fi
        
        # Validate a sample file structure
        SAMPLE_FILE=$(find src/content/rest-areas -name "*.md" -type f | head -1)
        if [ -n "$SAMPLE_FILE" ]; then
          echo "📝 Validating sample file: $SAMPLE_FILE"
          
          # Check for required frontmatter fields
          if ! grep -q "^title:" "$SAMPLE_FILE"; then
            echo "❌ Missing title field in frontmatter"
            exit 1
          fi
          
          if ! grep -q "^coordinates:" "$SAMPLE_FILE"; then
            echo "❌ Missing coordinates field in frontmatter"
            exit 1
          fi
          
          echo "✅ Content validation passed"
        fi

    - name: Run Astro build test
      if: steps.check_changes.outputs.has_changes == 'true'
      run: |
        echo "🏗️  Testing Astro build with new content..."
        npm run build
        echo "✅ Astro build test passed"

    - name: Commit and push changes
      if: steps.check_changes.outputs.has_changes == 'true'
      run: |
        echo "📝 Committing changes..."

        # Configure git
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

        # Add changes
        git add src/content/rest-areas/

        # Check if there are changes to commit
        if git diff --staged --quiet; then
          echo "ℹ️  No changes to commit"
          exit 0
        fi

        # Create commit message with summary
        COMMIT_MSG="🔄 Auto-sync content from Google Sheets"

        if [ -f "temp/sheets-export/export-summary.json" ]; then
          TOTAL_RECORDS=$(cat temp/sheets-export/export-summary.json | jq -r '.summary.totalRecords')
          LANGUAGES=$(cat temp/sheets-export/export-summary.json | jq -r '.languages | keys | join(", ")')
          COMMIT_MSG="$COMMIT_MSG

    - Total records: $TOTAL_RECORDS
    - Languages: $LANGUAGES
    - Timestamp: $(date -u +"%Y-%m-%d %H:%M:%S UTC")"
        fi

        # Commit changes
        git commit -m "$COMMIT_MSG"

        # Push changes
        git push origin main

        echo "✅ Changes committed and pushed successfully"

    - name: Create Pull Request (if configured)
      if: steps.check_changes.outputs.has_changes == 'true' && vars.CREATE_PR == 'true'
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: "🔄 Auto-sync content from Google Sheets"
        title: "Auto-sync content from Google Sheets"
        body: |
          ## 🔄 Automated Content Sync
          
          This PR contains automatically synchronized content from Google Sheets.
          
          ### Summary
          - **Timestamp**: ${{ steps.check_changes.outputs.timestamp }}
          - **Content Hash**: `${{ steps.check_changes.outputs.new_hash }}`
          - **Previous Hash**: `${{ steps.check_changes.outputs.current_hash }}`
          
          ### Changes
          - Updated rest area content from Google Sheets
          - Generated markdown files for multiple languages
          - Validated content structure and Astro compatibility
          
          ### Quality Checks
          - ✅ Content validation passed
          - ✅ Astro build test passed
          - ✅ Schema compatibility verified
          
          This PR was automatically created by the content sync workflow.
        branch: auto-sync-content
        delete-branch: true

    - name: Notify on failure
      if: failure()
      run: |
        echo "❌ Content sync workflow failed"
        
        # Create failure summary
        echo "## ❌ Content Sync Failed" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "The automated content sync from Google Sheets has failed." >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Timestamp**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> $GITHUB_STEP_SUMMARY
        echo "**Workflow**: ${{ github.workflow }}" >> $GITHUB_STEP_SUMMARY
        echo "**Run ID**: ${{ github.run_id }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "Please check the workflow logs for details and resolve any issues." >> $GITHUB_STEP_SUMMARY

    - name: Success summary
      if: success()
      run: |
        echo "✅ Content sync workflow completed successfully"
        
        # Create success summary
        echo "## ✅ Content Sync Successful" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ "${{ steps.check_changes.outputs.has_changes }}" = "true" ]; then
          echo "Content has been successfully synchronized from Google Sheets." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Changes**: Content updated" >> $GITHUB_STEP_SUMMARY
        else
          echo "No content changes were detected." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Changes**: No updates needed" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "**Timestamp**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> $GITHUB_STEP_SUMMARY
        echo "**Workflow**: ${{ github.workflow }}" >> $GITHUB_STEP_SUMMARY
        echo "**Run ID**: ${{ github.run_id }}" >> $GITHUB_STEP_SUMMARY

    - name: Cleanup
      if: always()
      run: |
        echo "🧹 Cleaning up temporary files..."
        rm -rf temp/
        echo "✅ Cleanup completed"
