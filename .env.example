# Google Analytics 4 Configuration
# Get your Measurement ID from Google Analytics 4 property settings
# Format: G-XXXXXXXXXX
PUBLIC_GA4_MEASUREMENT_ID=

# Google Ads Configuration (for advertising campaigns)
# Get your Google Ads Customer ID from Google Ads account
# Format: 123-456-7890
PUBLIC_GOOGLE_ADS_ID=

# Google Ads Conversion Tracking
# Get your Conversion ID from Google Ads conversion actions
# Format: AW-*********/AbC-D_efG-h_i-jk
PUBLIC_GOOGLE_ADS_CONVERSION_ID=

# Google AdSense Configuration (for revenue generation)
# Get your AdSense Publisher ID from AdSense account
# Format: ca-pub-****************
PUBLIC_ADSENSE_PUBLISHER_ID=

# AdSense Auto Ads (recommended for revenue optimization)
# Set to 'true' to enable auto ads
PUBLIC_ADSENSE_AUTO_ADS=true

# AdSense Ad Units Configuration
# Set to 'true' to enable manual ad units
PUBLIC_ADSENSE_MANUAL_ADS=true

# Privacy and Consent Configuration
# Set to 'true' to enable cookie consent banner
PUBLIC_ENABLE_COOKIE_CONSENT=true

# Analytics Configuration
# Set to 'true' to enable analytics in production
PUBLIC_ENABLE_ANALYTICS=true

# Development Configuration
# Set to 'true' to enable analytics in development (for testing)
PUBLIC_ENABLE_DEV_ANALYTICS=false

# Google Sheets Integration Configuration
# Google Sheets ID for content synchronization
# Get this from the Google Sheets URL: https://docs.google.com/spreadsheets/d/{SHEET_ID}/edit
GOOGLE_SHEETS_ID=

# Google Service Account Credentials (JSON format)
# Create a service account in Google Cloud Console and download the JSON key
# For GitHub Actions, set this as a repository secret
# For local development, save the JSON file as 'google-credentials.json' in the project root
GOOGLE_CREDENTIALS_JSON=

# Content Sync Configuration
# Set to 'true' to create pull requests instead of direct commits (recommended for production)
CREATE_PR=false

# Backup Configuration
# Set to 'true' to create backups before content updates
ENABLE_CONTENT_BACKUP=true
