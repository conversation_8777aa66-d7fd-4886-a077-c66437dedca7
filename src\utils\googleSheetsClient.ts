import { google } from 'googleapis';
import fs from 'fs/promises';
import path from 'path';

// Configure Google Sheets API client
export async function getGoogleSheetsClient() {
  // For service account authentication (recommended)
  const auth = new google.auth.GoogleAuth({
    keyFile: path.join(process.cwd(), 'google-credentials.json'),
    scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
  });
  
  const client = await auth.getClient();
  return google.sheets({ version: 'v4', auth: client });
}

// Fetch data from Google Sheet
export async function fetchSheetData(spreadsheetId, range) {
  const sheets = await getGoogleSheetsClient();
  const response = await sheets.spreadsheets.values.get({
    spreadsheetId,
    range,
  });
  
  return response.data.values;
}