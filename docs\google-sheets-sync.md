# Google Sheets Content Sync System

## Overview

The Google Sheets Content Sync System enables non-technical users to update rest area content through Google Sheets while maintaining the existing Astro Content Layer API structure and multilingual support. The system automatically syncs content, validates data, and generates markdown files compatible with the existing codebase.

## Features

- 🔄 **Automated Sync**: Daily scheduled sync from Google Sheets to markdown files
- 🌍 **Multilingual Support**: Supports English and Polish content generation
- ✅ **Data Validation**: Comprehensive validation against existing schema
- 🔒 **Safe Updates**: Backup system and validation before content updates
- 🚀 **GitHub Actions**: Fully automated workflow with error handling
- 📊 **Rate Limiting**: Respects Google Sheets API limits
- 🔍 **Change Detection**: Only updates when content actually changes

## Architecture

```mermaid
graph TD
    A[Google Sheets] --> B[GitHub Actions Workflow]
    B --> C[Export Script]
    C --> D[JSON Files]
    D --> E[Validation]
    E --> F[Markdown Generation]
    F --> G[Astro Content Layer]
    G --> H[Website]
    
    I[Backup System] --> F
    J[Error Handling] --> B
    K[Rate Limiting] --> C
```

## Setup Instructions

### 1. Google Cloud Project Setup

1. **Create a Google Cloud Project**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select an existing one

2. **Enable Google Sheets API**:
   - Navigate to "APIs & Services" > "Library"
   - Search for "Google Sheets API"
   - Click "Enable"

3. **Create Service Account**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "Service Account"
   - Fill in the service account details
   - Download the JSON key file

### 2. Google Sheets Setup

1. **Create or Prepare Your Google Sheet**:
   - Create a new Google Sheet or use an existing one
   - Ensure it has separate sheets for each language (e.g., "English", "Polish")

2. **Required Column Structure**:
   ```
   Required Columns:
   - rest_area_id
   - title
   - latitude
   - longitude
   - road_class
   - road_number
   - km_marker
   - region
   - location
   - country
   
   Optional Columns:
   - description_short
   - address_line
   - work_hours
   - contact_info
   - rating
   - toilets_available (yes/no)
   - wifi (yes/no)
   - gas_station_available (yes/no)
   - restaurant_bistro_available (yes/no)
   - shop (yes/no)
   - playground (yes/no)
   - showers_available (yes/no)
   - car_wash_available (yes/no)
   - ev_charging_station (yes/no)
   - security_personnel_on_site (yes/no)
   - cctv_video_surveillance (yes/no)
   - area_lighting (yes/no)
   - accommodation_available (yes/no)
   - fenced_area (yes/no)
   - administrator
   - mop_category
   - travel_direction
   - parking_spaces_cars
   - parking_spaces_trucks_tir
   - parking_spaces_buses
   - parking_spaces_dangerous
   - toilets_accessible (yes/no)
   - ev_charger_details
   - last_verified_date
   - data_source_url
   - internal_notes
   ```

3. **Share the Sheet**:
   - Share the Google Sheet with the service account email
   - Grant "Viewer" permissions

### 3. GitHub Repository Setup

1. **Add Repository Secrets**:
   - Go to your GitHub repository
   - Navigate to "Settings" > "Secrets and variables" > "Actions"
   - Add the following secrets:
     - `GOOGLE_CREDENTIALS_JSON`: The entire JSON content from the service account key file
     - `GOOGLE_SHEETS_ID`: The ID from your Google Sheets URL

2. **Configure Variables (Optional)**:
   - Add repository variable `CREATE_PR` set to `true` if you want pull requests instead of direct commits

### 4. Local Development Setup

1. **Install Dependencies**:
   ```bash
   npm install googleapis
   ```

2. **Environment Configuration**:
   - Copy `.env.example` to `.env`
   - Fill in the required values:
     ```env
     GOOGLE_SHEETS_ID=your_sheet_id_here
     ```

3. **Service Account Key**:
   - Save the service account JSON key as `google-credentials.json` in the project root
   - Add `google-credentials.json` to `.gitignore` (already included)

## Usage

### Automated Sync (Production)

The system automatically syncs content daily at 6 AM UTC. You can also trigger it manually:

1. Go to "Actions" tab in your GitHub repository
2. Select "Sync Content from Google Sheets" workflow
3. Click "Run workflow"
4. Optionally configure:
   - Force update even if no changes detected
   - Specific languages to sync

### Manual Sync (Development)

1. **Export from Google Sheets**:
   ```bash
   node scripts/export-from-sheets.js
   ```

2. **Generate Markdown Files**:
   ```bash
   node scripts/generate-from-json.js
   ```

3. **Test the Build**:
   ```bash
   npm run build
   ```

## Data Validation

The system performs comprehensive validation:

### Sheet Structure Validation
- Checks for required columns
- Validates column names and structure
- Reports missing or incorrectly named columns

### Data Content Validation
- **Required Fields**: Ensures all required fields are present
- **Coordinates**: Validates latitude/longitude are valid numbers
- **Boolean Fields**: Checks yes/no fields have valid values
- **Data Types**: Validates numeric fields contain numbers

### Schema Compatibility
- Validates against Astro Content Layer API schema
- Ensures generated markdown is compatible with existing system
- Tests Astro build process with new content

## Error Handling

### Common Issues and Solutions

1. **"Google credentials not found"**:
   - Ensure `GOOGLE_CREDENTIALS_JSON` secret is set correctly
   - For local development, ensure `google-credentials.json` file exists

2. **"Sheet is missing required columns"**:
   - Check your Google Sheet has all required column headers
   - Ensure column names match exactly (case-insensitive)

3. **"No data found in range"**:
   - Verify the sheet name in the configuration
   - Ensure the sheet has data rows (not just headers)

4. **"Invalid latitude/longitude"**:
   - Check coordinate values are valid decimal numbers
   - Ensure no empty coordinate fields for required entries

### Backup and Recovery

The system automatically creates backups before updating content:

- Backups are stored in `temp/content-backup/`
- Each backup includes timestamp for easy identification
- Original content is preserved if sync fails

## Monitoring and Maintenance

### GitHub Actions Monitoring

- Check the "Actions" tab for workflow status
- Review workflow logs for detailed information
- Set up notifications for failed workflows

### Content Quality Checks

- Review generated content periodically
- Validate that translations are accurate
- Ensure all required fields are populated

### Performance Considerations

- The system respects Google Sheets API rate limits
- Large datasets may take longer to process
- Consider splitting very large sheets into smaller ones

## Troubleshooting

### Debug Mode

Enable debug logging by setting environment variables:

```bash
DEBUG=true node scripts/export-from-sheets.js
```

### Manual Validation

Test individual components:

```bash
# Test Google Sheets connection
node -e "import('./src/utils/googleSheetsClient.ts').then(m => m.fetchSheetData('YOUR_SHEET_ID', 'English!A1:Z1'))"

# Validate specific sheet structure
node scripts/export-from-sheets.js --validate-only

# Test markdown generation
node scripts/generate-from-json.js --dry-run
```

### Common Fixes

1. **Clear temporary files**:
   ```bash
   rm -rf temp/
   ```

2. **Reset to backup**:
   ```bash
   # Find backup directory
   ls temp/content-backup/
   # Restore from backup
   cp -r temp/content-backup/TIMESTAMP/* src/content/rest-areas/
   ```

3. **Force full sync**:
   - Use the "force_update" option in GitHub Actions
   - Or delete existing content and re-run sync

## Security Considerations

- Service account has read-only access to Google Sheets
- Credentials are stored as GitHub secrets (encrypted)
- No sensitive data is logged or exposed
- Backup files are temporary and cleaned up automatically

## Contributing

When modifying the sync system:

1. Test changes locally first
2. Validate with sample data
3. Ensure backward compatibility
4. Update documentation as needed
5. Test the full workflow in a fork before merging

## Support

For issues with the Google Sheets sync system:

1. Check the troubleshooting section above
2. Review GitHub Actions logs
3. Validate your Google Sheets structure
4. Ensure all required secrets are configured
5. Test individual components manually
